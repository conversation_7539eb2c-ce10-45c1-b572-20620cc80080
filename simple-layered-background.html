<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Layered Background System</title>
    <meta name="color-scheme" content="light dark">
    
    <style>
        /* CSS Custom Properties for Theme Management */
        :root {
            /* Light Theme Variables */
            --bg-base-light: rgba(255, 255, 255, 0.30);
            --bg-overlay-light: linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.30) 100%);
            --blob-1-light: #8561C5;
            --blob-2-light: #BBBEC5;
            --blob-3-light: #2563EB;
            
            /* Dark Theme Variables */
            --bg-base-dark: rgba(30, 30, 30, 0.10);
            --bg-overlay-dark: rgba(30, 30, 30, 1.0);
            --blob-1-dark: #673AB7;
            --blob-2-dark: #341864;
            --blob-3-dark: #00376B;
            
            /* Animation Variables */
            --transition-duration: 0.3s;
            --blur-amount: 250px;
            --backdrop-blur: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            overflow-x: hidden;
        }

        /* Base Container Setup */
        .layered-background-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            transition: all var(--transition-duration) ease-in-out;
        }

        /* Layer 1: Base Background */
        .layered-background-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="light"] .layered-background-container::before {
            background: var(--bg-base-light);
        }

        [data-theme="dark"] .layered-background-container::before {
            background: var(--bg-base-dark);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        /* Layer 2: Gradient Blobs */
        .gradient-blobs {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            pointer-events: none;
        }

        .blob {
            position: absolute;
            filter: blur(var(--blur-amount));
            transition: all var(--transition-duration) ease-in-out;
        }

        .blob-1 {
            left: 0px;
            top: 191px;
            width: 477px;
            height: 381px;
            border-radius: 477px;
        }

        .blob-2 {
            left: 482px;
            top: 517px;
            width: 585px;
            height: 381px;
            border-radius: 585px;
        }

        .blob-3 {
            left: 1025px;
            top: 0px;
            width: 477px;
            height: 381px;
            border-radius: 477px;
        }

        /* Light theme blob colors */
        [data-theme="light"] .blob-1 { background: var(--blob-1-light); }
        [data-theme="light"] .blob-2 { background: var(--blob-2-light); }
        [data-theme="light"] .blob-3 { background: var(--blob-3-light); }

        /* Dark theme blob colors and positions */
        [data-theme="dark"] .blob-1 { background: var(--blob-1-dark); }
        [data-theme="dark"] .blob-2 { 
            background: var(--blob-3-dark);
            left: 482px;
            top: 517px;
        }
        [data-theme="dark"] .blob-3 { 
            background: var(--blob-2-dark);
            left: 1025px;
            top: 0px;
        }

        /* Layer 3: Overlay */
        .layered-background-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 3;
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="light"] .layered-background-container::after {
            background: var(--bg-overlay-light);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        [data-theme="dark"] .layered-background-container::after {
            background: var(--bg-overlay-dark);
        }

        /* Content Styles */
        .content {
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            padding: 2rem;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 3rem;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .content-card {
            background: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.05);
        }

        .title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: #333;
            transition: color var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .title {
            color: #fff;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #666;
            transition: color var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .subtitle {
            color: #ccc;
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #333;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        [data-theme="dark"] .theme-toggle {
            color: #fff;
            background: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .theme-toggle:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 1440px) {
            .blob { transform: scale(0.8); }
            .blob-1 { left: -50px; top: 150px; }
            .blob-2 { left: 300px; top: 400px; }
            .blob-3 { left: 800px; top: -50px; }
        }

        @media (max-width: 768px) {
            .blob { 
                transform: scale(0.6); 
                filter: blur(150px);
            }
            .blob-1 { left: -100px; top: 100px; }
            .blob-2 { left: 150px; top: 300px; }
            .blob-3 { left: 400px; top: -100px; }
            
            .theme-toggle {
                top: 10px;
                right: 10px;
                padding: 8px 16px;
                font-size: 12px;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition: none !important;
            }
        }

        @media (prefers-contrast: high) {
            :root {
                --blur-amount: 100px;
                --backdrop-blur: 5px;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="layered-background-container">
        <!-- Layer 2: Gradient Blobs -->
        <div class="gradient-blobs">
            <div class="blob blob-1"></div>
            <div class="blob blob-2"></div>
            <div class="blob blob-3"></div>
        </div>

        <!-- Theme Toggle -->
        <button class="theme-toggle" id="themeToggle">🌙 Dark Mode</button>

        <!-- Content -->
        
    </div>

    <script>
        let currentTheme = 'light';
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');

        function updateTheme() {
            body.setAttribute('data-theme', currentTheme);
            themeToggle.textContent = currentTheme === 'light' ? '🌙 Dark Mode' : '☀️ Light Mode';
        }

        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            updateTheme();
        }

        // Auto-detect system theme preference
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        if (mediaQuery.matches) {
            currentTheme = 'dark';
            updateTheme();
        }

        mediaQuery.addEventListener('change', (e) => {
            currentTheme = e.matches ? 'dark' : 'light';
            updateTheme();
        });

        themeToggle.addEventListener('click', toggleTheme);

        // Keyboard shortcut (T key)
        document.addEventListener('keydown', (e) => {
            if (e.key === 't' || e.key === 'T') {
                toggleTheme();
            }
        });
    </script>
</body>
</html>
