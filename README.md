# Layered Gradient Background System

A sophisticated React application featuring layered gradient backgrounds with animated blobs, smooth theme transitions, and responsive design.

## 🎨 Features

- **Layered Architecture**: Three distinct layers (base, gradient blobs, overlay) with proper z-index management
- **Theme Switching**: Smooth transitions between light and dark themes using CSS custom properties
- **Responsive Design**: Adapts to different screen sizes with proportional scaling
- **Performance Optimized**: Uses CSS transforms and `will-change` for smooth animations
- **Accessibility**: Supports `prefers-reduced-motion` and `prefers-contrast` media queries
- **Cross-browser Compatible**: Works on Chrome 88+, Firefox 78+, Safari 14+

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   ```

## 📁 Project Structure

```
src/
├── components/
│   └── LayeredBackground.jsx    # Main background component
├── styles/
│   └── layered-background.css   # Complete CSS system
├── App.jsx                      # Demo application
├── App.css                      # Global styles
└── main.jsx                     # React entry point
```

## 🎯 Usage Examples

### Basic Usage

```jsx
import LayeredBackground from './components/LayeredBackground';

function App() {
  return (
    <LayeredBackground theme="light">
      <div className="content-overlay">
        <h1>Your Content Here</h1>
      </div>
    </LayeredBackground>
  );
}
```

### With Theme Toggle

```jsx
import LayeredBackground, { ThemeToggle } from './components/LayeredBackground';
import { useState } from 'react';

function App() {
  const [theme, setTheme] = useState('light');

  return (
    <LayeredBackground theme={theme}>
      <ThemeToggle currentTheme={theme} onThemeChange={setTheme} />
      <div className="content-overlay">
        <h1>Your Content Here</h1>
      </div>
    </LayeredBackground>
  );
}
```

### Auto Theme Detection

```jsx
<LayeredBackground theme="auto">
  {/* Content automatically adapts to system theme */}
</LayeredBackground>
```

## 🎨 Theme Specifications

### Light Theme
- **Base Layer**: `rgba(255, 255, 255, 0.30)`
- **Blob Colors**: Purple (`#8561C5`), Gray (`#BBBEC5`), Blue (`#2563EB`)
- **Overlay**: Linear gradient with backdrop blur

### Dark Theme
- **Base Layer**: `rgba(30, 30, 30, 0.10)` with backdrop blur
- **Blob Colors**: Deep Purple (`#673AB7`), Dark Purple (`#341864`), Dark Blue (`#00376B`)
- **Overlay**: Solid dark overlay (`rgba(30, 30, 30, 1.0)`)

## 🔧 Customization

### CSS Custom Properties

The system uses CSS custom properties for easy customization:

```css
:root {
  --transition-duration: 0.3s;
  --blur-amount: 250px;
  --backdrop-blur: 15px;
  
  /* Light theme colors */
  --blob-1-light: #8561C5;
  --blob-2-light: #BBBEC5;
  --blob-3-light: #2563EB;
  
  /* Dark theme colors */
  --blob-1-dark: #673AB7;
  --blob-2-dark: #341864;
  --blob-3-dark: #00376B;
}
```

### Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `theme` | `'light' \| 'dark' \| 'auto'` | `'auto'` | Theme mode |
| `children` | `ReactNode` | - | Content to render over background |
| `className` | `string` | `''` | Additional CSS classes |
| `enableAnimations` | `boolean` | `true` | Enable/disable animations |

## 📱 Responsive Breakpoints

- **Desktop**: 1440px+ (full size)
- **Tablet**: 768px - 1440px (80% scale)
- **Mobile**: <768px (60% scale, reduced blur)

## ♿ Accessibility

- Respects `prefers-reduced-motion` for users who prefer less animation
- Supports `prefers-contrast` for high contrast mode
- Proper ARIA labels on interactive elements
- Maintains sufficient contrast ratios

## 🎭 Browser Support

- Chrome 88+
- Firefox 78+
- Safari 14+
- Edge 88+

## 📄 License

MIT License - feel free to use in your projects!

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test across different browsers
5. Submit a pull request

## 🐛 Known Issues

- `backdrop-filter` may not work in older browsers (graceful degradation included)
- Performance may vary on lower-end devices with complex blur effects

## 💡 Tips

- Use the `content-overlay` class for content that needs to stand out over the background
- Adjust blur amounts in CSS custom properties for different visual effects
- Test theme transitions in different lighting conditions
- Consider using `will-change: transform` sparingly for performance
