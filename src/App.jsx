import React, { useState } from 'react';
import { BackgroundDemo } from './components/LayeredBackground';
import AdvancedDemo from './components/AdvancedDemo';
import './App.css';

function App() {
  const [currentDemo, setCurrentDemo] = useState('advanced');

  return (
    <div className="App">
      {/* Demo Selector */}
      <div style={{
        position: 'fixed',
        top: '20px',
        left: '20px',
        zIndex: 1001,
        display: 'flex',
        gap: '10px'
      }}>
        <button
          onClick={() => setCurrentDemo('basic')}
          style={{
            padding: '8px 16px',
            borderRadius: '8px',
            border: 'none',
            background: currentDemo === 'basic' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            color: '#333',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}
        >
          Basic Demo
        </button>
        <button
          onClick={() => setCurrentDemo('advanced')}
          style={{
            padding: '8px 16px',
            borderRadius: '8px',
            border: 'none',
            background: currentDemo === 'advanced' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            color: '#333',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}
        >
          Advanced Demo
        </button>
      </div>

      {/* Render Current Demo */}
      {currentDemo === 'basic' ? <BackgroundDemo /> : <AdvancedDemo />}
    </div>
  );
}

export default App;
