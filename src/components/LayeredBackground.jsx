import React, { useState, useEffect } from 'react';
import '../styles/layered-background.css';

/**
 * LayeredBackground Component
 * 
 * A sophisticated background system with animated gradient blobs
 * that supports light and dark themes with smooth transitions.
 * 
 * Props:
 * - theme: 'light' | 'dark' | 'auto' (default: 'auto')
 * - children: React nodes to render over the background
 * - className: Additional CSS classes
 * - enableAnimations: boolean (default: true)
 */
const LayeredBackground = ({ 
  theme = 'auto', 
  children, 
  className = '',
  enableAnimations = true 
}) => {
  const [currentTheme, setCurrentTheme] = useState('light');

  // Auto theme detection based on system preference
  useEffect(() => {
    if (theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      setCurrentTheme(mediaQuery.matches ? 'dark' : 'light');

      const handleChange = (e) => {
        setCurrentTheme(e.matches ? 'dark' : 'light');
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      setCurrentTheme(theme);
    }
  }, [theme]);

  // Apply theme to document root for CSS custom properties
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', currentTheme);
  }, [currentTheme]);

  const containerClasses = [
    'layered-background-container',
    className,
    !enableAnimations && 'no-animations'
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={containerClasses}
      data-theme={currentTheme}
      style={{
        '--transition-duration': enableAnimations ? '0.3s' : '0s'
      }}
    >
      {/* Layer 2: Gradient Blobs */}
      <div className="gradient-blobs">
        <div className="blob-1" />
        <div className="blob-2" />
        <div className="blob-3" />
      </div>

      {/* Content Layer */}
      {children}
    </div>
  );
};

/**
 * ThemeToggle Component
 * 
 * A utility component for toggling between light and dark themes
 */
export const ThemeToggle = ({ currentTheme, onThemeChange }) => {
  const handleToggle = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    onThemeChange(newTheme);
  };

  return (
    <button
      onClick={handleToggle}
      className="theme-toggle-button"
      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} theme`}
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1000,
        padding: '12px 16px',
        borderRadius: '8px',
        border: 'none',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        color: currentTheme === 'light' ? '#333' : '#fff',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        fontSize: '14px',
        fontWeight: '500'
      }}
    >
      {currentTheme === 'light' ? '🌙 Dark' : '☀️ Light'}
    </button>
  );
};

/**
 * BackgroundDemo Component
 * 
 * A complete demo showing the layered background with theme switching
 */
export const BackgroundDemo = () => {
  const [theme, setTheme] = useState('light');

  return (
    <LayeredBackground theme={theme}>
      <ThemeToggle currentTheme={theme} onThemeChange={setTheme} />
      
      <div className="content-overlay" style={{
        maxWidth: '800px',
        margin: '10vh auto',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: '700',
          marginBottom: '1rem',
          color: theme === 'light' ? '#333' : '#fff'
        }}>
          Layered Background System
        </h1>
        
        <p style={{
          fontSize: '1.2rem',
          lineHeight: '1.6',
          marginBottom: '2rem',
          color: theme === 'light' ? '#666' : '#ccc'
        }}>
          A sophisticated background system with animated gradient blobs,
          smooth theme transitions, and responsive design.
        </p>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginTop: '2rem'
        }}>
          <div className="feature-card" style={{
            padding: '1.5rem',
            borderRadius: '12px',
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <h3 style={{ color: theme === 'light' ? '#333' : '#fff', marginBottom: '0.5rem' }}>
              Theme Switching
            </h3>
            <p style={{ color: theme === 'light' ? '#666' : '#ccc', fontSize: '0.9rem' }}>
              Smooth transitions between light and dark themes with CSS custom properties
            </p>
          </div>
          
          <div className="feature-card" style={{
            padding: '1.5rem',
            borderRadius: '12px',
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <h3 style={{ color: theme === 'light' ? '#333' : '#fff', marginBottom: '0.5rem' }}>
              Responsive Design
            </h3>
            <p style={{ color: theme === 'light' ? '#666' : '#ccc', fontSize: '0.9rem' }}>
              Adapts to different screen sizes with proportional scaling
            </p>
          </div>
          
          <div className="feature-card" style={{
            padding: '1.5rem',
            borderRadius: '12px',
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <h3 style={{ color: theme === 'light' ? '#333' : '#fff', marginBottom: '0.5rem' }}>
              Performance Optimized
            </h3>
            <p style={{ color: theme === 'light' ? '#666' : '#ccc', fontSize: '0.9rem' }}>
              Uses CSS transforms and will-change for smooth animations
            </p>
          </div>
        </div>
      </div>
    </LayeredBackground>
  );
};

export default LayeredBackground;
