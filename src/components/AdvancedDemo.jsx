import React, { useState, useEffect } from 'react';
import LayeredBackground, { ThemeToggle } from './LayeredBackground';

/**
 * AdvancedDemo Component
 * 
 * Showcases all features of the layered background system including:
 * - Theme switching
 * - Animated blobs
 * - Glass card effects
 * - Responsive design
 * - Accessibility features
 */
const AdvancedDemo = () => {
  const [theme, setTheme] = useState('light');
  const [animationsEnabled, setAnimationsEnabled] = useState(true);
  const [currentSection, setCurrentSection] = useState(0);

  const sections = [
    {
      title: "Welcome to the Future",
      subtitle: "Experience next-generation background design",
      content: "This layered background system combines modern CSS techniques with React components to create stunning visual experiences."
    },
    {
      title: "Smooth Transitions",
      subtitle: "Watch themes change seamlessly",
      content: "CSS custom properties enable instant theme switching with smooth transitions across all elements."
    },
    {
      title: "Responsive & Accessible",
      subtitle: "Works everywhere, for everyone",
      content: "Fully responsive design with accessibility features including reduced motion support and high contrast mode."
    }
  ];

  // Auto-advance sections for demo
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSection((prev) => (prev + 1) % sections.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [sections.length]);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setAnimationsEnabled(!mediaQuery.matches);

    const handleChange = (e) => {
      setAnimationsEnabled(!e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const currentSectionData = sections[currentSection];

  return (
    <LayeredBackground 
      theme={theme} 
      enableAnimations={animationsEnabled}
      className={animationsEnabled ? 'animated-blobs' : ''}
    >
      {/* Theme and Animation Controls */}
      <div style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1000,
        display: 'flex',
        gap: '10px'
      }}>
        <button
          onClick={() => setAnimationsEnabled(!animationsEnabled)}
          className="glass-card"
          style={{
            padding: '8px 12px',
            border: 'none',
            background: 'rgba(255, 255, 255, 0.1)',
            color: theme === 'light' ? '#333' : '#fff',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500'
          }}
        >
          {animationsEnabled ? '⏸️ Pause' : '▶️ Play'} Animations
        </button>
        <ThemeToggle currentTheme={theme} onThemeChange={setTheme} />
      </div>

      {/* Main Content */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: '2rem',
        textAlign: 'center'
      }}>
        {/* Hero Section */}
        <div className="content-overlay" style={{
          maxWidth: '900px',
          marginBottom: '3rem',
          transform: `translateY(${currentSection * -10}px)`,
          transition: 'transform 0.5s ease-in-out'
        }}>
          <h1 className="text-primary" style={{
            fontSize: 'clamp(2.5rem, 5vw, 4rem)',
            fontWeight: '800',
            marginBottom: '1rem',
            lineHeight: '1.1'
          }}>
            {currentSectionData.title}
          </h1>
          
          <p className="text-secondary" style={{
            fontSize: 'clamp(1.1rem, 2.5vw, 1.5rem)',
            marginBottom: '2rem',
            fontWeight: '300'
          }}>
            {currentSectionData.subtitle}
          </p>
          
          <p className="text-primary" style={{
            fontSize: '1.1rem',
            lineHeight: '1.6',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            {currentSectionData.content}
          </p>
        </div>

        {/* Feature Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '2rem',
          maxWidth: '1200px',
          width: '100%'
        }}>
          {/* Performance Card */}
          <div className="glass-card" style={{
            textAlign: 'left',
            transform: `translateX(${currentSection === 0 ? '0' : '-20px'})`,
            opacity: currentSection === 0 ? 1 : 0.7,
            transition: 'all 0.5s ease-in-out'
          }}>
            <div style={{
              fontSize: '2rem',
              marginBottom: '1rem'
            }}>⚡</div>
            <h3 className="text-primary" style={{
              fontSize: '1.3rem',
              fontWeight: '600',
              marginBottom: '0.5rem'
            }}>
              High Performance
            </h3>
            <p className="text-secondary" style={{
              fontSize: '0.95rem',
              lineHeight: '1.5'
            }}>
              Optimized CSS transforms and will-change properties ensure smooth 60fps animations
              even on lower-end devices.
            </p>
          </div>

          {/* Customization Card */}
          <div className="glass-card" style={{
            textAlign: 'left',
            transform: `translateX(${currentSection === 1 ? '0' : '-20px'})`,
            opacity: currentSection === 1 ? 1 : 0.7,
            transition: 'all 0.5s ease-in-out'
          }}>
            <div style={{
              fontSize: '2rem',
              marginBottom: '1rem'
            }}>🎨</div>
            <h3 className="text-primary" style={{
              fontSize: '1.3rem',
              fontWeight: '600',
              marginBottom: '0.5rem'
            }}>
              Fully Customizable
            </h3>
            <p className="text-secondary" style={{
              fontSize: '0.95rem',
              lineHeight: '1.5'
            }}>
              CSS custom properties make it easy to adjust colors, timing, and effects
              to match your brand.
            </p>
          </div>

          {/* Accessibility Card */}
          <div className="glass-card" style={{
            textAlign: 'left',
            transform: `translateX(${currentSection === 2 ? '0' : '-20px'})`,
            opacity: currentSection === 2 ? 1 : 0.7,
            transition: 'all 0.5s ease-in-out'
          }}>
            <div style={{
              fontSize: '2rem',
              marginBottom: '1rem'
            }}>♿</div>
            <h3 className="text-primary" style={{
              fontSize: '1.3rem',
              fontWeight: '600',
              marginBottom: '0.5rem'
            }}>
              Accessible Design
            </h3>
            <p className="text-secondary" style={{
              fontSize: '0.95rem',
              lineHeight: '1.5'
            }}>
              Respects user preferences for reduced motion and high contrast,
              ensuring everyone can enjoy your content.
            </p>
          </div>
        </div>

        {/* Section Indicators */}
        <div style={{
          display: 'flex',
          gap: '8px',
          marginTop: '3rem'
        }}>
          {sections.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSection(index)}
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                background: currentSection === index 
                  ? (theme === 'light' ? '#333' : '#fff')
                  : (theme === 'light' ? '#ccc' : '#666'),
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                opacity: currentSection === index ? 1 : 0.5
              }}
              aria-label={`Go to section ${index + 1}`}
            />
          ))}
        </div>

        {/* Technical Specs */}
        <div className="glass-card" style={{
          marginTop: '3rem',
          maxWidth: '800px',
          textAlign: 'left'
        }}>
          <h4 className="text-primary" style={{
            fontSize: '1.1rem',
            fontWeight: '600',
            marginBottom: '1rem'
          }}>
            Technical Specifications
          </h4>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            fontSize: '0.9rem'
          }}>
            <div>
              <strong className="text-primary">Browser Support:</strong>
              <br />
              <span className="text-secondary">Chrome 88+, Firefox 78+, Safari 14+</span>
            </div>
            <div>
              <strong className="text-primary">Performance:</strong>
              <br />
              <span className="text-secondary">60fps animations, GPU accelerated</span>
            </div>
            <div>
              <strong className="text-primary">Accessibility:</strong>
              <br />
              <span className="text-secondary">WCAG 2.1 AA compliant</span>
            </div>
            <div>
              <strong className="text-primary">Framework:</strong>
              <br />
              <span className="text-secondary">React 18+ with Vite</span>
            </div>
          </div>
        </div>
      </div>
    </LayeredBackground>
  );
};

export default AdvancedDemo;
