/* ========================================
   Layered Gradient Background System
   ======================================== */

/* CSS Custom Properties for Theme Management */
:root {
  /* Light Theme Variables */
  --bg-base-light: rgba(255, 255, 255, 0.30);
  --bg-overlay-light: linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.30) 100%);
  --blob-1-light: #8561C5;
  --blob-2-light: #BBBEC5;
  --blob-3-light: #2563EB;
  
  /* Dark Theme Variables */
  --bg-base-dark: rgba(30, 30, 30, 0.10);
  --bg-overlay-dark: rgba(30, 30, 30, 1.0);
  --blob-1-dark: #673AB7;
  --blob-2-dark: #341864;
  --blob-3-dark: #00376B;
  
  /* Animation Variables */
  --transition-duration: 0.3s;
  --transition-timing: ease-in-out;
  --blur-amount: 250px;
  --backdrop-blur: 15px;
}

/* ========================================
   Base Container Setup
   ======================================== */

.layered-background-container {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 900px;
  overflow: hidden;
  transition: all var(--transition-duration) var(--transition-timing);
}

/* Ensure proper stacking context */
.layered-background-container * {
  position: relative;
  z-index: 10; /* Content above background layers */
}

/* ========================================
   Layer 1: Base Background Layer (z-index: 1)
   ======================================== */

.layered-background-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  transition: all var(--transition-duration) var(--transition-timing);
}

/* Light theme base layer */
[data-theme="light"] .layered-background-container::before {
  background: var(--bg-base-light);
}

/* Dark theme base layer */
[data-theme="dark"] .layered-background-container::before {
  background: var(--bg-base-dark);
  backdrop-filter: blur(var(--backdrop-blur));
  -webkit-backdrop-filter: blur(var(--backdrop-blur));
}

/* ========================================
   Layer 2: Gradient Blobs (z-index: 2)
   ======================================== */

.gradient-blobs {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

/* Blob 1 - Purple/Deep Purple */
.blob-1 {
  position: absolute;
  left: 0px;
  top: 191px;
  width: 477px;
  height: 381px;
  border-radius: 477px;
  filter: blur(var(--blur-amount));
  transform-origin: center;
  transition: all var(--transition-duration) var(--transition-timing);
  will-change: transform, background-color;
}

[data-theme="light"] .blob-1 {
  background: var(--blob-1-light);
}

[data-theme="dark"] .blob-1 {
  background: var(--blob-1-dark);
}

/* Blob 2 - Gray/Dark Blue (position changes between themes) */
.blob-2 {
  position: absolute;
  width: 585px;
  height: 381px;
  border-radius: 585px;
  filter: blur(var(--blur-amount));
  transform-origin: center;
  transition: all var(--transition-duration) var(--transition-timing);
  will-change: transform, background-color;
}

[data-theme="light"] .blob-2 {
  background: var(--blob-2-light);
  left: 482px;
  top: 517px;
}

[data-theme="dark"] .blob-2 {
  background: var(--blob-3-dark);
  left: 482px;
  top: 517px;
}

/* Blob 3 - Blue/Dark Purple (position and size changes between themes) */
.blob-3 {
  position: absolute;
  border-radius: 477px;
  filter: blur(var(--blur-amount));
  transform-origin: center;
  transition: all var(--transition-duration) var(--transition-timing);
  will-change: transform, background-color;
}

[data-theme="light"] .blob-3 {
  background: var(--blob-3-light);
  left: 1025px;
  top: 0px;
  width: 477px;
  height: 381px;
}

[data-theme="dark"] .blob-3 {
  background: var(--blob-2-dark);
  left: 1025px;
  top: 0px;
  width: 477px;
  height: 381px;
}

/* ========================================
   Layer 3: Overlay Layer (z-index: 3)
   ======================================== */

.layered-background-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  transition: all var(--transition-duration) var(--transition-timing);
}

/* Light theme overlay */
[data-theme="light"] .layered-background-container::after {
  background: var(--bg-overlay-light);
  backdrop-filter: blur(var(--backdrop-blur));
  -webkit-backdrop-filter: blur(var(--backdrop-blur));
}

/* Dark theme overlay */
[data-theme="dark"] .layered-background-container::after {
  background: var(--bg-overlay-dark);
}

/* ========================================
   Responsive Design
   ======================================== */

/* Tablet and smaller desktop screens */
@media (max-width: 1440px) {
  .layered-background-container {
    min-height: 100vh;
  }
  
  .blob-1, .blob-2, .blob-3 {
    transform: scale(0.8);
  }
  
  .blob-1 {
    left: -50px;
    top: 150px;
  }
  
  [data-theme="light"] .blob-2,
  [data-theme="dark"] .blob-2 {
    left: 300px;
    top: 400px;
  }
  
  [data-theme="light"] .blob-3,
  [data-theme="dark"] .blob-3 {
    left: 800px;
    top: -50px;
  }
}

/* Mobile screens */
@media (max-width: 768px) {
  .blob-1, .blob-2, .blob-3 {
    transform: scale(0.6);
    filter: blur(150px);
  }
  
  .blob-1 {
    left: -100px;
    top: 100px;
  }
  
  [data-theme="light"] .blob-2,
  [data-theme="dark"] .blob-2 {
    left: 150px;
    top: 300px;
  }
  
  [data-theme="light"] .blob-3,
  [data-theme="dark"] .blob-3 {
    left: 400px;
    top: -100px;
  }
}

/* ========================================
   Accessibility & Performance
   ======================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .layered-background-container,
  .layered-background-container::before,
  .layered-background-container::after,
  .blob-1,
  .blob-2,
  .blob-3 {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --blur-amount: 100px;
    --backdrop-blur: 5px;
  }
}

/* ========================================
   Optional Animations
   ======================================== */

/* Subtle floating animation for blobs */
@keyframes float-1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

@keyframes float-2 {
  0%, 100% { transform: translateX(0px) rotate(0deg); }
  50% { transform: translateX(15px) rotate(0.5deg); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(10px); }
  75% { transform: translateY(15px) translateX(-10px); }
}

/* Apply animations to blobs (optional - can be enabled via class) */
.animated-blobs .blob-1 {
  animation: float-1 20s ease-in-out infinite;
}

.animated-blobs .blob-2 {
  animation: float-2 25s ease-in-out infinite;
}

.animated-blobs .blob-3 {
  animation: float-3 30s ease-in-out infinite;
}

/* Disable animations for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .animated-blobs .blob-1,
  .animated-blobs .blob-2,
  .animated-blobs .blob-3 {
    animation: none;
  }
}

/* ========================================
   Utility Classes
   ======================================== */

.theme-light {
  data-theme: light;
}

.theme-dark {
  data-theme: dark;
}

/* Content overlay helpers */
.content-overlay {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem;
  transition: all var(--transition-duration) var(--transition-timing);
}

[data-theme="dark"] .content-overlay {
  background: rgba(0, 0, 0, 0.2);
}

/* Glass card effect */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all var(--transition-duration) var(--transition-timing);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

[data-theme="dark"] .glass-card {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .glass-card:hover {
  background: rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Text utilities for better readability over background */
.text-primary {
  color: var(--text-primary);
  transition: color var(--transition-duration) var(--transition-timing);
}

.text-secondary {
  color: var(--text-secondary);
  transition: color var(--transition-duration) var(--transition-timing);
}

:root {
  --text-primary: #333;
  --text-secondary: #666;
}

[data-theme="dark"] {
  --text-primary: #fff;
  --text-secondary: #ccc;
}
