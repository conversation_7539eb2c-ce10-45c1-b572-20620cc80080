{"name": "layered-background-system", "version": "1.0.0", "description": "A sophisticated React application with layered gradient backgrounds and animated blobs", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.8"}, "browserslist": {"production": ["Chrome >= 88", "Firefox >= 78", "Safari >= 14", "Edge >= 88"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}