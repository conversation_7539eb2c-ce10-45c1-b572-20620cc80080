<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Layered Background System</title>
    <meta name="description" content="A sophisticated React application with layered gradient backgrounds and animated blobs" />
    
    <!-- Preload critical CSS for better performance -->
    <link rel="preload" href="/src/styles/layered-background.css" as="style" />
    
    <!-- Color scheme meta for better theme detection -->
    <meta name="color-scheme" content="light dark" />
    
    <style>
      /* Critical CSS for initial render */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background: #f5f5f5;
      }
      
      /* Loading state */
      #root:empty::before {
        content: 'Loading...';
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-size: 1.2rem;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
