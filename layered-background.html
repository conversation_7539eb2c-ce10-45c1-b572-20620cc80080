/* ====== Frame (1440 × 900) ====== */
.bg-1440x900 {
  position: relative;
  width: 1440px;
  height: 900px;
  overflow: hidden;     /* crop blur overflow */
  isolation: isolate;   /* keep blending contained */
}

/* Base surface fill via theme variable */
.bg-1440x900::before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--surface);
  z-index: 0;
}

/* ====== Theme Tokens ====== */
.theme--light {
  --surface: #FFFFFF;
  /* Light blobs */
  --blob-left:   #8561C5; /* x=0,   y=191, 477×381 */
  --blob-right:  #2563EB; /* x=1025,y=0,   477×381 */
  --blob-bottom: #BBBEC5; /* x=482, y=517, 585×381 */
}

.theme--dark {
  --surface: #1E1E1E;
  /* Dark blobs */
  --blob-left:   #67BA37; /* x=0,   y=191, 477×381 */
  --blob-right:  #341864; /* x=1025,y=0,   477×381 */
  --blob-bottom: #00376B; /* x=482, y=517, 585×381 */
}

/* ====== Blobs Layer ====== */
.blobs {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.blob {
  position: absolute;
  border-radius: 50%;
  filter: blur(110px);         /* “layer blur” look */
  will-change: filter, transform;
  flex-shrink: 0;              /* matches your spec */
}

/* Left blob — 477×381 at (0,191) */
.blob--left {
  width: 477px;
  height: 381px;
  left: 0px;
  top: 191px;
  background: var(--blob-left);
}

/* Right blob — 477×381 at (1025,0) */
.blob--right {
  width: 477px;
  height: 381px;
  left: 1025px;
  top: 0px;
  background: var(--blob-right);
}

/* Bottom blob — 585×381 at (482,517) */
.blob--bottom {
  width: 585px;
  height: 381px;
  left: 482px;
  top: 517px;
  background: var(--blob-bottom);
}

/* ====== Top Overlay Rectangle (same size as frame) ====== */
.overlay {
  position: absolute;
  inset: 0;
  z-index: 2;
  pointer-events: none; /* decorative only */
}

/* Light mode: gradient + blur */
.theme--light .overlay {
  background: linear-gradient(0deg,
              rgba(255, 255, 255, 0.50) 0%,
              rgba(255, 255, 255, 0.30) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px); /* Safari */
}

/* Dark mode: 10% #1E1E1E, no blur */
.theme--dark .overlay {
  background: rgba(30, 30, 30, 0.10);
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}
