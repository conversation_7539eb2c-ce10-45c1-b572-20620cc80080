<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layered Background System - Pure HTML/CSS</title>
    <meta name="description" content="A sophisticated layered gradient background system with animated blobs">
    <meta name="color-scheme" content="light dark">
    
    <style>
        /* ========================================
           CSS Custom Properties for Theme Management
           ======================================== */
        :root {
            /* Light Theme Variables */
            --bg-base-light: rgba(255, 255, 255, 0.30);
            --bg-overlay-light: linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.30) 100%);
            --blob-1-light: #8561C5;
            --blob-2-light: #BBBEC5;
            --blob-3-light: #2563EB;
            
            /* Dark Theme Variables */
            --bg-base-dark: rgba(30, 30, 30, 0.10);
            --bg-overlay-dark: rgba(30, 30, 30, 1.0);
            --blob-1-dark: #673AB7;
            --blob-2-dark: #341864;
            --blob-3-dark: #00376B;
            
            /* Animation Variables */
            --transition-duration: 0.3s;
            --transition-timing: ease-in-out;
            --blur-amount: 250px;
            --backdrop-blur: 15px;
            
            /* Text Colors */
            --text-primary: #333;
            --text-secondary: #666;
        }

        [data-theme="dark"] {
            --text-primary: #fff;
            --text-secondary: #ccc;
        }

        /* ========================================
           Global Styles
           ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
            width: 100%;
            min-height: 100vh;
        }

        /* ========================================
           Base Container Setup
           ======================================== */
        .layered-background-container {
            position: relative;
            width: 100%;
            height: 100vh;
            min-height: 900px;
            overflow: hidden;
            transition: all var(--transition-duration) var(--transition-timing);
        }

        /* Ensure proper stacking context */
        .layered-background-container > * {
            position: relative;
            z-index: 10;
        }

        /* ========================================
           Layer 1: Base Background Layer (z-index: 1)
           ======================================== */
        .layered-background-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            transition: all var(--transition-duration) var(--transition-timing);
        }

        /* Light theme base layer */
        [data-theme="light"] .layered-background-container::before {
            background: var(--bg-base-light);
        }

        /* Dark theme base layer */
        [data-theme="dark"] .layered-background-container::before {
            background: var(--bg-base-dark);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        /* ========================================
           Layer 2: Gradient Blobs (z-index: 2)
           ======================================== */
        .gradient-blobs {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            pointer-events: none;
        }

        /* Blob 1 - Purple/Deep Purple */
        .blob-1 {
            position: absolute;
            left: 0px;
            top: 191px;
            width: 477px;
            height: 381px;
            border-radius: 477px;
            filter: blur(var(--blur-amount));
            transform-origin: center;
            transition: all var(--transition-duration) var(--transition-timing);
            will-change: transform, background-color;
        }

        [data-theme="light"] .blob-1 {
            background: var(--blob-1-light);
        }

        [data-theme="dark"] .blob-1 {
            background: var(--blob-1-dark);
        }

        /* Blob 2 - Gray/Dark Blue */
        .blob-2 {
            position: absolute;
            width: 585px;
            height: 381px;
            border-radius: 585px;
            filter: blur(var(--blur-amount));
            transform-origin: center;
            transition: all var(--transition-duration) var(--transition-timing);
            will-change: transform, background-color;
        }

        [data-theme="light"] .blob-2 {
            background: var(--blob-2-light);
            left: 482px;
            top: 517px;
        }

        [data-theme="dark"] .blob-2 {
            background: var(--blob-3-dark);
            left: 482px;
            top: 517px;
        }

        /* Blob 3 - Blue/Dark Purple */
        .blob-3 {
            position: absolute;
            border-radius: 477px;
            filter: blur(var(--blur-amount));
            transform-origin: center;
            transition: all var(--transition-duration) var(--transition-timing);
            will-change: transform, background-color;
        }

        [data-theme="light"] .blob-3 {
            background: var(--blob-3-light);
            left: 1025px;
            top: 0px;
            width: 477px;
            height: 381px;
        }

        [data-theme="dark"] .blob-3 {
            background: var(--blob-2-dark);
            left: 1025px;
            top: 0px;
            width: 477px;
            height: 381px;
        }

        /* ========================================
           Layer 3: Overlay Layer (z-index: 3)
           ======================================== */
        .layered-background-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 3;
            transition: all var(--transition-duration) var(--transition-timing);
        }

        /* Light theme overlay */
        [data-theme="light"] .layered-background-container::after {
            background: var(--bg-overlay-light);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        /* Dark theme overlay */
        [data-theme="dark"] .layered-background-container::after {
            background: var(--bg-overlay-dark);
        }

        /* ========================================
           Optional Animations
           ======================================== */
        @keyframes float-1 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        @keyframes float-2 {
            0%, 100% { transform: translateX(0px) rotate(0deg); }
            50% { transform: translateX(15px) rotate(0.5deg); }
        }

        @keyframes float-3 {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-15px) translateX(10px); }
            75% { transform: translateY(15px) translateX(-10px); }
        }

        /* Apply animations to blobs */
        .animated-blobs .blob-1 {
            animation: float-1 20s ease-in-out infinite;
        }

        .animated-blobs .blob-2 {
            animation: float-2 25s ease-in-out infinite;
        }

        .animated-blobs .blob-3 {
            animation: float-3 30s ease-in-out infinite;
        }

        /* ========================================
           Content Styles
           ======================================== */
        .content-overlay {
            position: relative;
            z-index: 10;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem;
            transition: all var(--transition-duration) var(--transition-timing);
        }

        [data-theme="dark"] .content-overlay {
            background: rgba(0, 0, 0, 0.2);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all var(--transition-duration) var(--transition-timing);
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        [data-theme="dark"] .glass-card {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .glass-card:hover {
            background: rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .text-primary {
            color: var(--text-primary);
            transition: color var(--transition-duration) var(--transition-timing);
        }

        .text-secondary {
            color: var(--text-secondary);
            transition: color var(--transition-duration) var(--transition-timing);
        }

        /* ========================================
           Control Buttons
           ======================================== */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .control-button {
            padding: 12px 16px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* ========================================
           Responsive Design
           ======================================== */
        @media (max-width: 1440px) {
            .layered-background-container {
                min-height: 100vh;
            }
            
            .blob-1, .blob-2, .blob-3 {
                transform: scale(0.8);
            }
            
            .blob-1 {
                left: -50px;
                top: 150px;
            }
            
            [data-theme="light"] .blob-2,
            [data-theme="dark"] .blob-2 {
                left: 300px;
                top: 400px;
            }
            
            [data-theme="light"] .blob-3,
            [data-theme="dark"] .blob-3 {
                left: 800px;
                top: -50px;
            }
        }

        @media (max-width: 768px) {
            .blob-1, .blob-2, .blob-3 {
                transform: scale(0.6);
                filter: blur(150px);
            }
            
            .blob-1 {
                left: -100px;
                top: 100px;
            }
            
            [data-theme="light"] .blob-2,
            [data-theme="dark"] .blob-2 {
                left: 150px;
                top: 300px;
            }
            
            [data-theme="light"] .blob-3,
            [data-theme="dark"] .blob-3 {
                left: 400px;
                top: -100px;
            }

            .controls {
                flex-direction: column;
                top: 10px;
                right: 10px;
            }

            .control-button {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* ========================================
           Accessibility
           ======================================== */
        @media (prefers-reduced-motion: reduce) {
            .layered-background-container,
            .layered-background-container::before,
            .layered-background-container::after,
            .blob-1,
            .blob-2,
            .blob-3,
            .content-overlay,
            .glass-card,
            .control-button {
                transition: none;
            }

            .animated-blobs .blob-1,
            .animated-blobs .blob-2,
            .animated-blobs .blob-3 {
                animation: none;
            }
        }

        @media (prefers-contrast: high) {
            :root {
                --blur-amount: 100px;
                --backdrop-blur: 5px;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="layered-background-container animated-blobs">
        <!-- Layer 2: Gradient Blobs -->
        <div class="gradient-blobs">
            <div class="blob-1"></div>
            <div class="blob-2"></div>
            <div class="blob-3"></div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <button class="control-button" id="animationToggle">⏸️ Pause Animations</button>
            <button class="control-button" id="themeToggle">🌙 Dark Theme</button>
        </div>

        <!-- Main Content -->
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; padding: 2rem; text-align: center;">
            <!-- Hero Section -->
            <div class="content-overlay" style="max-width: 900px; margin-bottom: 3rem;">
                <h1 class="text-primary" style="font-size: clamp(2.5rem, 5vw, 4rem); font-weight: 800; margin-bottom: 1rem; line-height: 1.1;">
                    Layered Background System
                </h1>

                <p class="text-secondary" style="font-size: clamp(1.1rem, 2.5vw, 1.5rem); margin-bottom: 2rem; font-weight: 300;">
                    Experience next-generation background design
                </p>

                <p class="text-primary" style="font-size: 1.1rem; line-height: 1.6; max-width: 600px; margin: 0 auto;">
                    This layered background system combines modern CSS techniques with smooth animations to create stunning visual experiences.
                </p>
            </div>

            <!-- Feature Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; max-width: 1200px; width: 100%;">
                <!-- Performance Card -->
                <div class="glass-card" style="text-align: left;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">⚡</div>
                    <h3 class="text-primary" style="font-size: 1.3rem; font-weight: 600; margin-bottom: 0.5rem;">
                        High Performance
                    </h3>
                    <p class="text-secondary" style="font-size: 0.95rem; line-height: 1.5;">
                        Optimized CSS transforms and will-change properties ensure smooth 60fps animations
                        even on lower-end devices.
                    </p>
                </div>

                <!-- Customization Card -->
                <div class="glass-card" style="text-align: left;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">🎨</div>
                    <h3 class="text-primary" style="font-size: 1.3rem; font-weight: 600; margin-bottom: 0.5rem;">
                        Fully Customizable
                    </h3>
                    <p class="text-secondary" style="font-size: 0.95rem; line-height: 1.5;">
                        CSS custom properties make it easy to adjust colors, timing, and effects
                        to match your brand.
                    </p>
                </div>

                <!-- Accessibility Card -->
                <div class="glass-card" style="text-align: left;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">♿</div>
                    <h3 class="text-primary" style="font-size: 1.3rem; font-weight: 600; margin-bottom: 0.5rem;">
                        Accessible Design
                    </h3>
                    <p class="text-secondary" style="font-size: 0.95rem; line-height: 1.5;">
                        Respects user preferences for reduced motion and high contrast,
                        ensuring everyone can enjoy your content.
                    </p>
                </div>
            </div>

            <!-- Technical Specs -->
            <div class="glass-card" style="margin-top: 3rem; max-width: 800px; text-align: left;">
                <h4 class="text-primary" style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem;">
                    Technical Specifications
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; font-size: 0.9rem;">
                    <div>
                        <strong class="text-primary">Browser Support:</strong>
                        <br>
                        <span class="text-secondary">Chrome 88+, Firefox 78+, Safari 14+</span>
                    </div>
                    <div>
                        <strong class="text-primary">Performance:</strong>
                        <br>
                        <span class="text-secondary">60fps animations, GPU accelerated</span>
                    </div>
                    <div>
                        <strong class="text-primary">Accessibility:</strong>
                        <br>
                        <span class="text-secondary">WCAG 2.1 AA compliant</span>
                    </div>
                    <div>
                        <strong class="text-primary">Framework:</strong>
                        <br>
                        <span class="text-secondary">Pure HTML/CSS/JS</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme and animation management
        let currentTheme = 'light';
        let animationsEnabled = true;

        // Get DOM elements
        const body = document.body;
        const container = document.querySelector('.layered-background-container');
        const themeToggle = document.getElementById('themeToggle');
        const animationToggle = document.getElementById('animationToggle');

        // Auto theme detection based on system preference
        function detectSystemTheme() {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            currentTheme = mediaQuery.matches ? 'dark' : 'light';
            updateTheme();

            // Listen for system theme changes
            mediaQuery.addEventListener('change', (e) => {
                currentTheme = e.matches ? 'dark' : 'light';
                updateTheme();
            });
        }

        // Update theme
        function updateTheme() {
            body.setAttribute('data-theme', currentTheme);
            themeToggle.textContent = currentTheme === 'light' ? '🌙 Dark Theme' : '☀️ Light Theme';
        }

        // Toggle theme manually
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            updateTheme();
        }

        // Toggle animations
        function toggleAnimations() {
            animationsEnabled = !animationsEnabled;

            if (animationsEnabled) {
                container.classList.add('animated-blobs');
                animationToggle.textContent = '⏸️ Pause Animations';
            } else {
                container.classList.remove('animated-blobs');
                animationToggle.textContent = '▶️ Play Animations';
            }
        }

        // Check for reduced motion preference
        function checkReducedMotion() {
            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (mediaQuery.matches) {
                animationsEnabled = false;
                container.classList.remove('animated-blobs');
                animationToggle.textContent = '▶️ Play Animations';
            }

            mediaQuery.addEventListener('change', (e) => {
                if (e.matches) {
                    animationsEnabled = false;
                    container.classList.remove('animated-blobs');
                    animationToggle.textContent = '▶️ Play Animations';
                }
            });
        }

        // Event listeners
        themeToggle.addEventListener('click', toggleTheme);
        animationToggle.addEventListener('click', toggleAnimations);

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            detectSystemTheme();
            checkReducedMotion();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 't' || e.key === 'T') {
                toggleTheme();
            }
            if (e.key === 'a' || e.key === 'A') {
                toggleAnimations();
            }
        });
    </script>
</body>
</html>
